---
title: 'How to Get Current Timestamp?'
date: '2025-07-27'
author: 'Devutils Team'
excerpt: "Learn how to get current Unix timestamps (seconds or milliseconds) in multiple programming languages, validate with DevUtils!"
tags: ["tutorial", "Unix Timestamp", "Unix Timestamp Converter"]
---

| Language    | Code                                                                                                                                                                 |
| ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Swift       | NSDate().timeIntervalSince1970                                                                                                                                       |
| Go          | import (<br/>  "time"<br/>)<br/>int64(time.Now().Unix())                                                                                                             |
| Java        | System.currentTimeMillis() / 1000                                                                                                                                    |
| C           | #include <sys/time.h><br/><br/>// ...<br/>struct timeval tv;<br/>gettimeofday(&tv, NULL);<br/>// 秒： tv.tv_sec<br/>// 毫秒： tv.tv_sec * 1000LL + tv.tv_usec / 1000 |
| JavaScript  | Math.round(new Date() / 1000)                                                                                                                                        |
| Objective-C | Math.round(new Date() / 1000)                                                                                                                                        |
| MySQL       | SELECT unix_timestamp(now())                                                                                                                                         |
| SQLite      | SELECT strftime('%s', 'now')                                                                                                                                         |
| Erlang      | calendar:datetime_to_gregorian_seconds(calendar:universal_time())-719528*24*3600.                                                                                    |
| PHP         | <?php<br/>// pure php<br/>time();                                                                                                                                    |
| Python      | import time<br/>time.time()                                                                                                                                          |
| Ruby        | Time.now.to_i                                                                                                                                                        |
| Shell       | date +%s                                                                                                                                                             |
| Groovy      |                                                                                                                                                                      |
| Lua         | os.time()                                                                                                                                                            |
| .NET/C#     | DateTimeOffset.UtcNow.ToUnixTimeSeconds();                                                                                                                           |
| Dart        | (new DateTime.now().millisecondsSinceEpoch / 1000).truncate()                                                                                                        |
