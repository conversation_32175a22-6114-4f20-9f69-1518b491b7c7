'use client';

import { getTranslation } from '@/lib/i18n';
import { Table, TableHeader, TableColumn, TableBody, TableCell, TableRow } from '@heroui/react'

export default function GetCurrentTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const tableData = [
    {
      language: "Swift",
      code: `NSDate().timeIntervalSince1970`
    },
    {
      language: "Go",
      code: `import (
  "time"
)
int64(time.Now().Unix())`
    },
    {
      language: "Java",
      code: `System.currentTimeMillis() / 1000`
    },
    {
      language: "C",
      code: `#include <sys/time.h>

// ...
struct timeval tv;
gettimeofday(&tv, NULL);
// Second： tv.tv_sec
// Millisecond： tv.tv_sec * 1000LL + tv.tv_usec / 1000`
    },
    {
      language: "JavaScript",
      code: `Math.round(new Date() / 1000)`
    },
    {
      language: "Objective-C",
      code: `[[NSDate date] timeIntervalSince1970]`
    },
    {
      language: "MySQL",
      code: `SELECT unix_timestamp(now())`
    },
    {
      language: "SQLite",
      code: `SELECT strftime('%s', 'now')`
    },
    {
      language: "Erlang",
      code: `calendar:datetime_to_gregorian_seconds(calendar:universal_time())-719528*24*3600.`
    },
    {
      language: "PHP",
      code: `<?php
// pure php
time();`
    },
    {
      language: "Python",
      code: `import time
time.time()`
    },
    {
      language: "Ruby",
      code: `Time.now.to_i`
    },
    {
      language: "Shell",
      code: `date +%s`
    },
    {
      language: "Groovy",
      code: `(new Date().time / 1000).longValue()`
    },
    {
      language: "Lua",
      code: `os.time()`
    },
    {
      language: ".NET/C#",
      code: `DateTimeOffset.UtcNow.ToUnixTimeSeconds();`
    },
    {
      language: "Dart",
      code: `(new DateTime.now().millisecondsSinceEpoch / 1000).truncate()`
    }
  ]

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('How to Get Currnet Timestamp in ...')}</h2>
      <Table hideHeader>
        <TableHeader>
          <TableColumn>Language</TableColumn>
          <TableColumn>Code</TableColumn>
        </TableHeader>
        <TableBody>
          {tableData.map((row) => (
            <TableRow key={row.language}>
              <TableCell className="font-medium">{row.language}</TableCell>
              <TableCell>
                <pre className="bg-gray-100 p-4 rounded-md">
                  <code>{row.code}</code>
                </pre>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  )
}
