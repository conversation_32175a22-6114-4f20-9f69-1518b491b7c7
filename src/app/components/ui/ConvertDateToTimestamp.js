'use client';

import { getTranslation } from '@/lib/i18n';
import { Table, TableHeader, TableColumn, TableBody, TableCell, TableRow } from '@heroui/react'

export default function ConvertDateToTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const tableData = [
    {
      language: "Swift",
      code: `let date = NSDate()
let timestamp = date.timeIntervalSince1970`
    },
    {
      language: "Go",
      code: `import (
    "time"
)
t := time.Now()
timestamp := t.Unix()`
    },
    {
      language: "Java",
      code: `java.util.Date date = new java.util.Date();
long timestamp = date.getTime() / 1000;`
    },
    {
      language: "C",
      code: `#include <time.h>
time_t t = time(NULL);
long timestamp = (long)t;`
    },
    {
      language: "JavaScript",
      code: `const date = new Date();
const timestamp = Math.round(date.getTime() / 1000);`
    },
    {
      language: "Objective-C",
      code: `NSDate *date = [NSDate date];
NSTimeInterval timestamp = [date timeIntervalSince1970];`
    },
    {
      language: "MySQL",
      code: `SELECT unix_timestamp('2021-06-26 12:00:00')`
    },
    {
      language: "SQLite",
      code: `SELECT strftime('%s', '2021-06-26 12:00:00')`
    },
    {
      language: "Erlang",
      code: `Datetime = calendar:universal_time(),
Timestamp = calendar:datetime_to_gregorian_seconds(Datetime) - 719528*24*3600.`
    },
    {
      language: "PHP",
      code: `<?php
// pure php
$date = new DateTime();
$timestamp = $date->getTimestamp();`
    },
    {
      language: "Python",
      code: `import time
from datetime import datetime
date = datetime.now()
timestamp = int(date.timestamp())`
    },
    {
      language: "Ruby",
      code: `time = Time.now
timestamp = time.to_i`
    },
    {
      language: "Shell",
      code: `date -d "2021-06-26 12:00:00" +%s`
    },
    {
      language: "Groovy",
      code: `Date date = new Date()
long timestamp = (date.time / 1000).longValue()`
    },
    {
      language: "Lua",
      code: `date = os.time({year=2021, month=6, day=26, hour=12, min=0, sec=0})
timestamp = date`
    },
    {
      language: ".NET/C#",
      code: `DateTimeOffset date = DateTimeOffset.Now;
long timestamp = date.ToUnixTimeSeconds();`
    },
    {
      language: "Dart",
      code: `DateTime date = DateTime.now();
int timestamp = (date.millisecondsSinceEpoch / 1000).truncate();`
    }
  ]

  return (
    <>
      <h2 className="text-2xl font-bold px-2 py-4">{t('How to Convert Date to Timestamp in ...')}</h2>
      <Table hideHeader>
        <TableHeader>
          <TableColumn>Language</TableColumn>
          <TableColumn>Code</TableColumn>
        </TableHeader>
        <TableBody>
          {tableData.map((row) => (
            <TableRow key={row.language}>
              <TableCell className="font-medium">{row.language}</TableCell>
              <TableCell>
                <pre className="bg-gray-100 p-4 rounded-md">
                  <code>{row.code}</code>
                </pre>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  );
}
